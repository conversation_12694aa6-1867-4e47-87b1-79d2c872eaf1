<?php
/**
 * 广州爱视传媒
 * Created by PhpStorm.
 * User: Ainetv
 * Date: 2020/9/29
 * Time: 12:00
 * PhpStorm
 */

function showResult($s = true, $d = "", $i = ""): string
{
    echo json_encode([
        "state"  => $s
        , "data" => $d
        , "info" => $i,
    ]);
    return "";
}

function result($s = true, $d = "", $i = "")
{
    $res        = new stdClass();
    $res->state = $s;
    $res->data  = $d;
    $res->info  = $i;
    return $res;
}

function returnInfo($s = true, $d = "", $i = "")
{
    return ["state" => $s, "data" => $d, "info" => $i];
}

/**
 * @param string $startDate
 * @param string $endDate
 * @return array
 */
function periodDate(string $startDate, string $endDate)
{
    $startTime = strtotime($startDate);
    $endTime   = strtotime($endDate);
    $arr       = [];
    while ($startTime <= $endTime) {
        $arr[]     = date('Y-m-d', $startTime);
        $startTime = strtotime('+1 day', $startTime);
    }
    return $arr;
}

function get_real_ip()
{
    $ip = FALSE;
    //客户端IP 或 NONE
    if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
        $ip = $_SERVER["HTTP_CLIENT_IP"];
    }
    //多重代理服务器下的客户端真实IP地址（可能伪造）,如果没有使用代理，此字段为空
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(", ", $_SERVER['HTTP_X_FORWARDED_FOR']);
        if ($ip) {
            array_unshift($ips, $ip);
            $ip = FALSE;
        }
        for ($i = 0; $i < count($ips); $i++) {
            if (!preg_match("^(10│172.16│192.168).", $ips[$i])) {
                $ip = $ips[$i];
                break;
            }
        }
    }
    //客户端IP 或 (最后一个)代理服务器 IP
    return ($ip ? $ip : $_SERVER['REMOTE_ADDR']);

}