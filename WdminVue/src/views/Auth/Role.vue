<template>
  <div id="rolecontainer">
    <el-form :inline="true" ref="searchForm">
      <el-form-item>
        <el-button type="success" size="small" :icon="Plus" plain @click="addNewRole">添加</el-button>
        <el-button type="info" size="small" :icon="Edit" plain @click="editRole">编辑</el-button>
        <el-button type="danger" size="small" :icon="Delete" plain @click="delRole">删除</el-button>
        <!--       <el-button type="primary" icon="el-icon-search" size="small" plain>搜索</el-button>-->
      </el-form-item>
    </el-form>
    <el-row type="flex" justify="space-between">
      <el-col :span="17">
        <el-table
            :data="RoleList"
            style="width: 100%;margin-bottom: 20px;"
            row-key="ID"
            border
            default-expand-all
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            highlight-current-row
            @current-change="rowClick">
          <el-table-column
              prop="ID"
              label="编号"
              sortable
              width="80">
          </el-table-column>
          <el-table-column
              prop="Title"
              label="角色"
              sortable
              width="180">
          </el-table-column>
          <el-table-column
              prop="Title"
              label="状态"
              sortable
              width="80" :formatter="format_role_state">
          </el-table-column>
          <el-table-column
              prop="Note"
              label="说明">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="7">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span><el-icon><Expand/></el-icon>权限树 </span>
            <span style="color:limegreen; font-weight: bold; font-size: 14px;">[ {{ CurrentRow == null ? "" : CurrentRow.Title }} ]</span>
            <el-button style="float: right;" size="small" plain
                       @click.native="selectAll(true)">全选
            </el-button>
            <el-button style="float: right;" size="small" plain @click.native="selectAll(false)">清空</el-button>
            <el-button :icon="Coin" style="float: right;" size="small" plain @click.native="updateRolePromission">保存</el-button>
          </div>

          <!--@node-click="treeNodeClick" @check-change="treeCheckChange"-->
          <el-tree
              :data="PermissionTree"
              :props="PermissionTreeProps"
              default-expand-all
              show-checkbox
              check-strictly
              ref="perTree"
              node-key="ID">
          </el-tree>
        </el-card>

      </el-col>
    </el-row>

    <el-dialog title="角色基础信息编辑" v-model="RoleFormVisible" :close-on-click-modal="false">
      <el-form :model="RoleForm" :rules="rules" ref="RoleForm">
        <el-form-item label="角色名称" prop="Title" label-width="100px">
          <el-input v-model="RoleForm.Title" placeholder="请填写角色名称"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="State" label-width="100px">
          <el-select v-model="RoleForm.State" placeholder="请选择">
            <el-option
                v-for="item in roleStates"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色说明" prop="Note" label-width="100px">
          <el-input v-model="RoleForm.Note"></el-input>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="cancelSave('RoleForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('RoleForm')">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {Plus, Edit, Delete, Coin}                                                   from '@element-plus/icons-vue'
import {DeleteRole, GetRoleList, GetRolePermission, SaveRole, UpdateRolePermission} from "@/api/modules/role";
import {GetPermissionTitle}                                                         from "@/api/modules/permission";

export default {
  name      : "Role",
  data      : () => {
    return {
      RoleFormVisible    : false,
      RoleForm           : {
        ID    : "",
        Title : "",
        Note  : "",
        State : "1",
        Parent: "1000",
        Left  : "0",
        Right : "0"
      },
      roleStates         : [
        {
          value: '1',
          label: '启用'
        }, {
          value: '0',
          label: '禁用'
        }
      ],
      TreeIdList         : [],
      RoleList           : [], // 角色列表
      RolePromission     : {}, //角色对应的权限列表
      PermissionList     : [], //权限列表
      PermissionTree     : [],
      PermissionTreeProps: {
        children: 'Children',
        label   : 'Title'
      },
      CurrentRow         : null  //当前选中的行
      , rules            : {
        Title: [
          {required: true, message: '请输入角色名称', trigger: 'blur'},
          {min: 3, max: 30, message: '长度在 3 到 30 个字符', trigger: 'blur'}
        ],
        Note : [
          {required: true, message: '请输入角色描述', trigger: 'blur'},
        ]
      }
    }
  }
  , computed: {
    Coin() {
      return Coin
    },
    Delete() {
      return Delete
    },
    Edit() {
      return Edit
    },
    Plus() {
      return Plus
    },  //属性计算
    fullName: {
      // getter
      get: function () {
        return this.firstName;
      },
      // setter
      set: function (newValue) {
        this.firstName = newValue;
      }
    }
  }
  , methods : {
    loadPermission() {
      return GetPermissionTitle().then((result) => {
        console.log("GetPermissionTitle", result)
        if (result.state) {
          this.formatPermissionTree(result.data);
          //Promise.reject(result.data)
          this.PermissionList = result.data;
        } else {
          this.$message.warning(result.data);
          Promise.resolve()
        }
      });
    }
    , formatPermissionTree(items) {
      let tree   = []; //格式化的树
      let tmpMap = {};  //临时扁平数据
      let item   = {};
      items.forEach((item) => {
        tmpMap[item.ID] = item;
        this.TreeIdList.push({"ID": item.ID});
      });
      for (let i in tmpMap) {
        item = tmpMap[i];
        if (Object.prototype.hasOwnProperty.call(tmpMap, item.ParentID)) {
          if (!Object.prototype.hasOwnProperty.call(tmpMap[item.ParentID], "Children")) {
            tmpMap[item.ParentID]["Children"] = [];
          }
          tmpMap[item.ParentID]["Children"].push(tmpMap[item.ID]);
        } else {
          tree.push(tmpMap[item.ID]);
        }
      }
      //tmpMap = null;
      this.PermissionTree = tree;
      //console.log("formatPermissionTree->tree", tree);
      return true;

    }
    , loadRoleList() {
      return GetRoleList().then((result) => {
        if (result.state) {
          this.RoleList = result.data;
          //Promise.reject(result.data)
        } else {
          this.$message.warning(result.data);
          Promise.resolve()
        }
      });
    }
    , format_role_state(row) {
      return row.State == "0" ? "禁用" : "启用"
    }
    , rowClick(row) {
      this.setPermissionTree([]);
      this.CurrentRow = row;
      //console.log("xxx", this.RolePromission[row.ID])
      if (Object.prototype.hasOwnProperty.call(this.RolePromission, row.ID)) {
        this.setPermissionTree(this.RolePromission[row.ID]);
        return true;
      }

      GetRolePermission({RoleID: row.ID}).then((result) => {
        if (result.state) {
          this.RolePromission[row.ID] = result.data;
          this.setPermissionTree(this.RolePromission[row.ID]);
        } else {
          this.$message.warning(result.data);
        }
      }).catch((error) => {
        this.$message.error({
                              message: "访问服务器失败: " + error.message,
                              //duration: 0
                            });
      });
    }
    , setPermissionTree(items) {
      let data = [];
      items.forEach((itm) => {
        data.push({"ID": itm});
      });
      this.$refs.perTree.setCheckedNodes(data);
    }
    , addNewRole() {
      this.CurrentRow      = null;
      this.RoleFormVisible = true;
      this.RoleForm.Title  = "";
      this.RoleForm.Note   = "";
      this.RoleForm.ID     = "";

    }
    , editRole() {
      this.RoleFormVisible = true;
      this.RoleForm.Title  = this.CurrentRow.Title;
      this.RoleForm.Note   = this.CurrentRow.Note;
      this.RoleForm.ID     = this.CurrentRow.ID;
    }
    , submitForm(formName) {
      let _this = this;
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          console.log('error submit!!');
          return false;
        }
        SaveRole(this.RoleForm).then((result) => {
          if (result.state) {
            _this.$message.success(result.info);
            if (_this.CurrentRow === null) {
              _this.RoleList.push(result.data);
            } else {
              _this.RoleList.forEach((item, i) => {
                if (item.ID == result.data.ID) {
                  _this.RoleList[i] = result.data;
                }
              });
            }
          } else {
            _this.$message.warning(result.info);
          }
        })
      });
    }
    , cancelSave(formName) {
      this.$refs[formName].resetFields();
      this.RoleFormVisible = false
    }
    , updateRolePromission() {
      if (this.CurrentRow === null) {
        this.$message.error("请选择您需要更改权限的角色");
        return false;
      }
      let checkedKeys = this.$refs.perTree.getCheckedKeys();
      if (checkedKeys === null) {
        this.$message.error("您还未选中任何的权限");
        return false;
      }
      let halfCheckedKeys = this.$refs.perTree.getHalfCheckedKeys();

      let data = [...checkedKeys, ...halfCheckedKeys];
      console.log("Role.updateRolePromission", data);

      UpdateRolePermission({
                             "RoleID"      : this.CurrentRow.ID,
                             "PermissionID": data
                           }).then((result) => {
        if (result.state) {
          delete this.RolePromission[this.CurrentRow.ID];
          this.$message.success(result.data);
        } else {
          this.$message.warning(result.data);
        }
      })
    }
    , selectAll(check) { // el-tree全选事件
      if (check === true) { // 全选
        console.log(this.TreeIdList);
        this.$refs.perTree.setCheckedNodes(this.TreeIdList)
      } else { // 取消选中
        this.$refs.perTree.setCheckedKeys([])
      }
    }
    , delRole() {
      let slf = this;

      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText : '取消',
        type             : 'warning'
      }).then(() => {
        let load = this.$loading({
                                   target: document.getElementById("rolecontainer")
                                 });
        return DeleteRole({ID: slf.CurrentRow.ID, Title: slf.CurrentRow.Title}).then((result) => {
          if (result.state) {
            slf.$message.success(result.data);
          } else {
            slf.$message.error(result.data);
          }
        }).catch((error) => {
          slf.$message.error(error.message)
        }).finally(() => {
          load.close();
        });
      });

    }
  }
  , watch   : {
    //监听
    //'$route'(to, from) {  //监听路由跳转
    //}
  },

  mounted: function () {
    let perPm       = this.loadPermission();
    let rolePm      = this.loadRoleList();
    let permiseList = [
      perPm, rolePm
    ];
    let load        = this.$loading({
                                      target: document.getElementById("rolecontainer")
                                    });
    Promise.all(permiseList).then(() => {
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      load.close();
    });
  },
}
</script>

<style scoped></style>