<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="h-16 bg-white shadow-sm flex items-center px-6">
      <div class="w-64 flex items-center space-x-4">
        <el-button type="primary" class="rounded-button whitespace-nowrap" @click="saveTheme">保存</el-button>
        <el-button class="rounded-button whitespace-nowrap" @click="showTemplateList">模版列表</el-button>
        <el-button class="rounded-button whitespace-nowrap" @click="showLayoutJSON">查看布局JSON</el-button>
      </div>
    </div>
    <!-- 主工作区 -->
    <div class="flex h-[calc(100vh-4rem)]">
      <!-- 左侧组件库 -->
      <div class="w-64 bg-white border-r p-4 overflow-y-auto mt-4">
        <div class="mb-6">
          <div class="text-lg font-semibold mb-3">组件库</div>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="媒体组件" name="1">
              <div class="grid grid-cols-2 gap-3 mt-3">
                <div
                  v-for="(item, index) in mediaComponents"
                  :key="index"
                  class="border rounded p-2 cursor-pointer hover:bg-gray-50 text-center"
                  @dragstart="dragStart($event, item)"
                  draggable="true"
                >
                  <i :class="item.icon" class="text-xl mb-1"></i>
                  <div class="text-sm">{{ item.name }}</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="信息组件" name="2">
              <div class="grid grid-cols-2 gap-3 mt-3">
                <div
                  v-for="(item, index) in infoComponents"
                  :key="index"
                  class="border rounded p-2 cursor-pointer hover:bg-gray-50 text-center"
                  @dragstart="dragStart($event, item)"
                  draggable="true"
                >
                  <i :class="item.icon" class="text-xl mb-1"></i>
                  <div class="text-sm">{{ item.name }}</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <!-- 中间画布区 -->
      <div
        class="flex-1 bg-gray-100 overflow-auto flex items-center justify-center"
        @drop="drop($event)"
        @dragover.prevent
      >
        <div
          class="bg-white shadow-lg relative"
          :style="{
            width: canvasSize.width + 'px',
            height: canvasSize.height + 'px'
          }"
        >
          <div
            v-for="(item, index) in canvasItems"
            :key="index"
            class="absolute border border-dashed border-transparent hover:border-blue-400"
            :style="{
              left: item.x + 'px',
              top: item.y + 'px',
              width: item.width + 'px',
              height: item.height + 'px',
              zIndex: item.zIndex
            }"
            @mousedown="selectItem(index)"
            @contextmenu.prevent="showDeleteConfirm(index)"
          >
            <div class="w-full h-full bg-white shadow-sm">
              <div class="flex items-center justify-center bg-gray-100 p-1">
                <span class="text-xs">{{ item.name }}</span>
              </div>
              <div class="p-2">
                <template v-if="item.type === 'text'">
                  <div class="text-center" :style="{ fontSize: item.fontSize + 'px', color: item.color }">
                    {{ item.content || '双击编辑文本' }}
                  </div>
                </template>
                <template v-else-if="item.type === 'image'">
                  <img
                    :src="item.src"
                    class="w-full h-full object-cover"
                    :style="{ borderRadius: item.shape === 'circle' ? '50%' : '0' }"
                  />
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧属性面板 -->
      <div class="w-80 bg-white border-l p-4 overflow-y-auto">
        <div v-if="selectedItem !== null">
          <el-tabs v-model="activePropertyTab">
            <el-tab-pane label="组件属性" name="properties">
              <div class="text-lg font-semibold mb-4">组件属性</div>
              <div class="mb-4">
                <div class="text-sm font-medium mb-1">位置和大小</div>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">X</div>
                    <el-input-number
                      v-model="canvasItems[selectedItem].x"
                      class="w-full"
                      :min="0"
                      :max="canvasSize.width"
                    />
                  </div>
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Y</div>
                    <el-input-number
                      v-model="canvasItems[selectedItem].y"
                      class="w-full"
                      :min="0"
                      :max="canvasSize.height"
                    />
                  </div>
                  <div>
                    <div class="text-xs text-gray-500 mb-1">宽度</div>
                    <el-input-number
                      v-model="canvasItems[selectedItem].width"
                      class="w-full"
                      :min="20"
                      :max="canvasSize.width"
                    />
                  </div>
                  <div>
                    <div class="text-xs text-gray-500 mb-1">高度</div>
                    <el-input-number
                      v-model="canvasItems[selectedItem].height"
                      class="w-full"
                      :min="20"
                      :max="canvasSize.height"
                    />
                  </div>
                </div>
              </div>
              <div class="mb-4">
                <div class="text-sm font-medium mb-1">图层顺序</div>
                <el-input-number
                  v-model="canvasItems[selectedItem].zIndex"
                  class="w-full"
                  :min="0"
                />
              </div>
              <template v-if="canvasItems[selectedItem].type === 'text'">
                <div class="mb-4">
                  <div class="text-sm font-medium mb-1">文本内容</div>
                  <el-input
                    v-model="canvasItems[selectedItem].content"
                    type="textarea"
                    placeholder="请输入文本内容"
                    :autosize="{ minRows: 2, maxRows: 5 }"
                  />
                </div>
                <div class="mb-4">
                  <div class="text-sm font-medium mb-1">字体大小</div>
                  <el-input-number
                    v-model="canvasItems[selectedItem].fontSize"
                    class="w-full"
                    :min="8"
                    :max="72"
                  />
                </div>
                <div class="mb-4">
                  <div class="text-sm font-medium mb-1">文本颜色</div>
                  <el-color-picker v-model="canvasItems[selectedItem].color" />
                </div>
              </template>
              <template v-else-if="canvasItems[selectedItem].type === 'image'">
                <div class="mb-4">
                  <div class="text-sm font-medium mb-1">图片形状</div>
                  <el-radio-group v-model="canvasItems[selectedItem].shape">
                    <el-radio label="rectangle">矩形</el-radio>
                    <el-radio label="circle">圆形</el-radio>
                  </el-radio-group>
                </div>
                <div class="mb-4">
                  <div class="text-sm font-medium mb-1">图片地址</div>
                  <el-input v-model="canvasItems[selectedItem].src" placeholder="请输入图片URL" />
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="组件内容" name="content">
              <div class="text-lg font-semibold mb-4">组件内容</div>
              <div v-if="canvasItems[selectedItem].type === 'image'" class="space-y-4">
                <div class="text-sm font-medium mb-1">预设图片</div>
                <div class="grid grid-cols-3 gap-2">
                  <div
                    v-for="(img, idx) in presetImages"
                    :key="idx"
                    class="border rounded p-1 cursor-pointer hover:border-blue-400"
                    @click="canvasItems[selectedItem].src = img"
                  >
                    <img :src="img" class="w-full h-16 object-cover" />
                  </div>
                </div>
                <el-upload
                  action="https://jsonplaceholder.typicode.com/posts/"
                  :show-file-list="false"
                  :on-success="handleImageUpload"
                >
                  <el-button type="primary" class="rounded-button whitespace-nowrap">上传图片</el-button>
                </el-upload>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-if="selectedItem === null" class="space-y-4">
          <div class="text-lg font-semibold mb-4">画布属性</div>
          <div class="mb-4">
            <div class="text-sm font-medium mb-1">网格显示</div>
            <el-switch v-model="showGrid" />
          </div>
          <div class="mb-4">
            <div class="text-sm font-medium mb-1">背景颜色</div>
            <el-color-picker v-model="canvasBackground" />
          </div>
          <div class="mb-4">
            <div class="text-sm font-medium mb-1">背景图片</div>
            <el-upload
              action="https://jsonplaceholder.typicode.com/posts/"
              :show-file-list="false"
              :on-success="handleBackgroundUpload"
            >
              <el-button type="primary" class="rounded-button whitespace-nowrap">上传背景图</el-button>
            </el-upload>
          </div>
        </div>
      </div>
    </div>
    <!-- 操作提示 -->
    <el-notification
      v-model:visible="showNotification"
      :title="notificationMessage"
      :message="notificationDescription"
      :duration="3000"
      @close="showNotification = false"
    />
    
    <!-- 模板列表对话框 -->
    <el-dialog v-model="templateListVisible" title="模板列表" width="60%">
      <div class="grid grid-cols-3 gap-4">
        <div v-for="(template, index) in templateList" :key="index" class="border rounded p-3 cursor-pointer hover:bg-gray-50">
          <div class="text-center font-medium mb-2">{{ template.name }}</div>
          <div class="bg-gray-100 h-32 flex items-center justify-center mb-2">
            <img v-if="template.thumbnail" :src="template.thumbnail" class="max-h-full max-w-full" />
            <div v-else class="text-gray-400">无预览图</div>
          </div>
          <div class="flex justify-between">
            <el-button size="small" @click="loadTemplate(template)">使用</el-button>
            <el-button size="small" type="danger" @click="deleteTemplate(template, index)">删除</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 查看JSON对话框 -->
    <el-dialog v-model="jsonViewVisible" title="布局JSON" width="60%">
      <pre class="bg-gray-100 p-4 rounded overflow-auto max-h-96">{{ JSON.stringify(canvasItems, null, 2) }}</pre>
      <template #footer>
        <el-button @click="jsonViewVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyJSON">复制</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import axios from '@/api/request';
import {
  ElButton,
  ElTabs,
  ElTabPane,
  ElInput,
  ElInputNumber,
  ElRadio,
  ElRadioGroup,
  ElColorPicker,
  ElSwitch,
  ElNotification,
  ElMessageBox,
  ElMessage,
  ElDialog,
  ElUpload
} from 'element-plus';

// 标签页状态
const activeTab = ref('1');
const activePropertyTab = ref('properties');
const selectedItem = ref<number | null>(null);

// 通知状态
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationDescription = ref('');

// 对话框状态
const templateListVisible = ref(false);
const jsonViewVisible = ref(false);

// 画布设置
const canvasSize = reactive({
  width: 800,
  height: 600
});
const showGrid = ref(false);
const canvasBackground = ref('#ffffff');
const canvasItems = reactive<any[]>([]);

// 预设图片
const presetImages = ref([
  'https://mastergo.com/ai/api/search-image?query=a modern minimalist product display with clean white background and soft lighting&width=300&height=300&orientation=squarish',
  'https://ai-public.mastergo.com/ai/img_res/4a2ebd8c6b66908dc063a1e6ec17ba30.jpg',
  'https://ai-public.mastergo.com/ai/img_res/0de19f9320912c2a696cb7e68194c401.jpg',
  'https://ai-public.mastergo.com/ai/img_res/d1202b3e18a76b46aec75f1c333a9387.jpg',
  'https://ai-public.mastergo.com/ai/img_res/d69101386742e3a72528e82a02b83b31.jpg',
  'https://ai-public.mastergo.com/ai/img_res/0011e6160b892365ae5f06d5300794b8.jpg'
]);

// 模板列表
const templateList = ref([
  { id: 1, name: '默认模板1', thumbnail: presetImages.value[0], data: [] },
  { id: 2, name: '默认模板2', thumbnail: presetImages.value[1], data: [] }
]);

// 组件库
const mediaComponents = [
  { name: '图片', type: 'image', icon: 'fas fa-image' },
  { name: '视频', type: 'video', icon: 'fas fa-video' },
  { name: 'Logo', type: 'logo', icon: 'fas fa-copyright' }
];

const infoComponents = [
  { name: '文本', type: 'text', icon: 'fas fa-font' },
  { name: '富文本', type: 'richtext', icon: 'fas fa-paragraph' },
  { name: '数字时钟', type: 'digitalclock', icon: 'fas fa-clock' },
  { name: '模拟时钟', type: 'analogclock', icon: 'fas fa-clock' },
  { name: '天气', type: 'weather', icon: 'fas fa-cloud-sun' },
  { name: '位置', type: 'location', icon: 'fas fa-map-marker-alt' },
  { name: '应用图标', type: 'appicon', icon: 'fas fa-mobile-alt' },
  { name: '滚动字幕', type: 'marquee', icon: 'fas fa-scroll' }
];

// 拖拽开始
const dragStart = (event: DragEvent, item: any) => {
  event.dataTransfer?.setData('text/plain', JSON.stringify(item));
};

// 放置组件
const drop = (event: DragEvent) => {
  event.preventDefault();
  const data = event.dataTransfer?.getData('text/plain');
  if (!data) return;
  
  const item = JSON.parse(data);
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  const x = event.clientX - rect.left - 50;
  const y = event.clientY - rect.top - 50;
  
  const newItem = {
    ...item,
    x,
    y,
    width: 100,
    height: 100,
    zIndex: canvasItems.length,
    content: '',
    fontSize: 16,
    color: '#000000',
    shape: 'rectangle',
    src: 'https://mastergo.com/ai/api/search-image?query=a modern minimalist product display with clean white background and soft lighting&width=300&height=300&orientation=squarish'
  };
  
  if (item.type === 'text') {
    newItem.content = '双击编辑文本';
  }
  
  canvasItems.push(newItem);
  showNotification.value = true;
  notificationMessage.value = '添加成功';
  notificationDescription.value = `已添加 ${item.name} 组件到画布`;
};

// 选择组件
const selectItem = (index: number) => {
  selectedItem.value = index;
};

// 删除组件确认
const showDeleteConfirm = (index: number) => {
  ElMessageBox.confirm('确定要删除这个组件吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    canvasItems.splice(index, 1);
    selectedItem.value = null;
    showNotification.value = true;
    notificationMessage.value = '删除成功';
    notificationDescription.value = '已从画布中移除该组件';
  }).catch(() => {});
};

// 图片上传处理
const handleImageUpload = (response: any) => {
  if (selectedItem.value !== null && canvasItems[selectedItem.value]) {
    // 实际项目中应该使用真实的上传接口返回的URL
    canvasItems[selectedItem.value].src = response.url || presetImages.value[0];
    ElMessage.success('图片上传成功');
  }
};

// 背景图片上传处理
const handleBackgroundUpload = (response: any) => {
  // 实际项目中应该使用真实的上传接口返回的URL
  ElMessage.success('背景图片上传成功');
};

// 保存主题
const saveTheme = () => {
  // 实际项目中应该调用API保存数据
  ElMessage.success('主题保存成功');
};

// 显示模板列表
const showTemplateList = () => {
  templateListVisible.value = true;
};

// 加载模板
const loadTemplate = (template: any) => {
  ElMessageBox.confirm('加载模板将覆盖当前画布内容，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实际项目中应该加载模板数据
    ElMessage.success(`已加载模板: ${template.name}`);
    templateListVisible.value = false;
  }).catch(() => {});
};

// 删除模板
const deleteTemplate = (template: any, index: number) => {
  ElMessageBox.confirm('确定要删除这个模板吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    templateList.value.splice(index, 1);
    ElMessage.success(`已删除模板: ${template.name}`);
  }).catch(() => {});
};

// 显示JSON视图
const showLayoutJSON = () => {
  jsonViewVisible.value = true;
};

// 复制JSON
const copyJSON = () => {
  const jsonStr = JSON.stringify(canvasItems, null, 2);
  navigator.clipboard.writeText(jsonStr).then(() => {
    ElMessage.success('JSON已复制到剪贴板');
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制');
  });
};
</script>

<style scoped>
/* 自定义样式 */
:deep(.el-tabs__header) {
  margin-bottom: 12px;
}

:deep(.el-tabs__item) {
  padding: 0 8px;
  height: 32px;
  line-height: 32px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-color-picker) {
  width: 100%;
}

.rounded-button {
  border-radius: 4px;
}
</style>