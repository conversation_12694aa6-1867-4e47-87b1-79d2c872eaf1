<template>
  <div class="theme-management">
    <!-- 顶部导航栏 -->
    <div class="top-toolbar">
      <div class="toolbar-actions">
        <el-button type="primary" @click="saveTheme">
          <el-icon><DocumentAdd /></el-icon>
          保存
        </el-button>
        <el-button @click="showTemplateList">
          <el-icon><Collection /></el-icon>
          模版列表
        </el-button>
        <el-button @click="showLayoutJSON">
          <el-icon><View /></el-icon>
          查看布局JSON
        </el-button>
      </div>
      <div class="toolbar-controls">
        <el-button
          :icon="leftPanelCollapsed ? 'Expand' : 'Fold'"
          circle
          size="small"
          @click="toggleLeftPanel"
          title="切换组件库"
        />
        <el-button
          :icon="rightPanelCollapsed ? 'Expand' : 'Fold'"
          circle
          size="small"
          @click="toggleRightPanel"
          title="切换属性面板"
        />
      </div>
    </div>

    <!-- 主工作区 -->
    <div class="main-workspace">
      <!-- 左侧组件库 -->
      <div
        class="left-panel"
        :class="{ 'collapsed': leftPanelCollapsed }"
        v-show="!leftPanelCollapsed || !isMobile"
      >
        <div class="panel-header">
          <h3 class="panel-title">组件库</h3>
        </div>
        <div class="panel-content">
          <el-tabs v-model="activeTab" class="component-tabs">
            <el-tab-pane label="媒体组件" name="1">
              <div class="component-grid">
                <div
                  v-for="(item, index) in mediaComponents"
                  :key="index"
                  class="component-item"
                  @dragstart="dragStart($event, item)"
                  draggable="true"
                >
                  <i :class="item.icon" class="component-icon"></i>
                  <div class="component-name">{{ item.name }}</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="信息组件" name="2">
              <div class="component-grid">
                <div
                  v-for="(item, index) in infoComponents"
                  :key="index"
                  class="component-item"
                  @dragstart="dragStart($event, item)"
                  draggable="true"
                >
                  <i :class="item.icon" class="component-icon"></i>
                  <div class="component-name">{{ item.name }}</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 中间画布区 -->
      <div class="canvas-area">
        <div class="canvas-container">
          <div class="canvas-toolbar">
            <div class="canvas-info">
              画布尺寸: {{ canvasSize.width }} × {{ canvasSize.height }}
            </div>
            <div class="canvas-controls">
              <el-button-group size="small">
                <el-button @click="zoomOut">
                  <el-icon><ZoomOut /></el-icon>
                </el-button>
                <el-button @click="resetZoom">{{ Math.round(zoomLevel * 100) }}%</el-button>
                <el-button @click="zoomIn">
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div
            class="canvas-viewport"
            @drop="drop($event)"
            @dragover.prevent
          >
            <div
              class="canvas"
              :style="{
                width: canvasSize.width + 'px',
                height: canvasSize.height + 'px',
                transform: `scale(${zoomLevel})`,
                backgroundColor: canvasBackground
              }"
            >
              <div
                v-for="(item, index) in canvasItems"
                :key="index"
                class="canvas-item"
                :class="{ 'selected': selectedItem === index }"
                :style="{
                  left: item.x + 'px',
                  top: item.y + 'px',
                  width: item.width + 'px',
                  height: item.height + 'px',
                  zIndex: item.zIndex
                }"
                @mousedown="selectItem(index)"
                @contextmenu.prevent="showDeleteConfirm(index)"
              >
                <div class="item-content">
                  <div class="item-header">
                    <span class="item-label">{{ item.name }}</span>
                  </div>
                  <div class="item-body">
                    <template v-if="item.type === 'text'">
                      <div
                        class="text-content"
                        :style="{
                          fontSize: item.fontSize + 'px',
                          color: item.color,
                          textAlign: 'center'
                        }"
                      >
                        {{ item.content || '双击编辑文本' }}
                      </div>
                    </template>
                    <template v-else-if="item.type === 'image'">
                      <img
                        :src="item.src"
                        class="image-content"
                        :style="{
                          borderRadius: item.shape === 'circle' ? '50%' : '4px',
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }"
                      />
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div
        class="right-panel"
        :class="{ 'collapsed': rightPanelCollapsed }"
        v-show="!rightPanelCollapsed || !isMobile"
      >
        <div class="panel-header">
          <h3 class="panel-title">属性配置</h3>
        </div>
        <div class="panel-content">
          <div v-if="selectedItem !== null">
            <el-tabs v-model="activePropertyTab" class="property-tabs">
              <el-tab-pane label="组件属性" name="properties">
                <div class="property-section">
                  <h4 class="section-title">位置和大小</h4>
                  <div class="property-grid">
                    <div class="property-item">
                      <label class="property-label">X坐标</label>
                      <el-input-number
                        v-model="canvasItems[selectedItem].x"
                        size="small"
                        :min="0"
                        :max="canvasSize.width"
                        controls-position="right"
                      />
                    </div>
                    <div class="property-item">
                      <label class="property-label">Y坐标</label>
                      <el-input-number
                        v-model="canvasItems[selectedItem].y"
                        size="small"
                        :min="0"
                        :max="canvasSize.height"
                        controls-position="right"
                      />
                    </div>
                    <div class="property-item">
                      <label class="property-label">宽度</label>
                      <el-input-number
                        v-model="canvasItems[selectedItem].width"
                        size="small"
                        :min="20"
                        :max="canvasSize.width"
                        controls-position="right"
                      />
                    </div>
                    <div class="property-item">
                      <label class="property-label">高度</label>
                      <el-input-number
                        v-model="canvasItems[selectedItem].height"
                        size="small"
                        :min="20"
                        :max="canvasSize.height"
                        controls-position="right"
                      />
                    </div>
                  </div>
                </div>

                <div class="property-section">
                  <h4 class="section-title">图层设置</h4>
                  <div class="property-item">
                    <label class="property-label">图层顺序</label>
                    <el-input-number
                      v-model="canvasItems[selectedItem].zIndex"
                      size="small"
                      :min="0"
                      controls-position="right"
                    />
                  </div>
                </div>

                <template v-if="canvasItems[selectedItem].type === 'text'">
                  <div class="property-section">
                    <h4 class="section-title">文本设置</h4>
                    <div class="property-item">
                      <label class="property-label">文本内容</label>
                      <el-input
                        v-model="canvasItems[selectedItem].content"
                        type="textarea"
                        placeholder="请输入文本内容"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        size="small"
                      />
                    </div>
                    <div class="property-item">
                      <label class="property-label">字体大小</label>
                      <el-input-number
                        v-model="canvasItems[selectedItem].fontSize"
                        size="small"
                        :min="8"
                        :max="72"
                        controls-position="right"
                      />
                    </div>
                    <div class="property-item">
                      <label class="property-label">文本颜色</label>
                      <el-color-picker
                        v-model="canvasItems[selectedItem].color"
                        size="small"
                        show-alpha
                      />
                    </div>
                  </div>
                </template>

                <template v-else-if="canvasItems[selectedItem].type === 'image'">
                  <div class="property-section">
                    <h4 class="section-title">图片设置</h4>
                    <div class="property-item">
                      <label class="property-label">图片形状</label>
                      <el-radio-group v-model="canvasItems[selectedItem].shape" size="small">
                        <el-radio label="rectangle">矩形</el-radio>
                        <el-radio label="circle">圆形</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="property-item">
                      <label class="property-label">图片地址</label>
                      <el-input
                        v-model="canvasItems[selectedItem].src"
                        placeholder="请输入图片URL"
                        size="small"
                      />
                    </div>
                  </div>
                </template>
              </el-tab-pane>

              <el-tab-pane label="组件内容" name="content">
                <div v-if="canvasItems[selectedItem].type === 'image'" class="property-section">
                  <h4 class="section-title">预设图片</h4>
                  <div class="preset-images">
                    <div
                      v-for="(img, idx) in presetImages"
                      :key="idx"
                      class="preset-image-item"
                      @click="canvasItems[selectedItem].src = img"
                    >
                      <img :src="img" alt="预设图片" />
                    </div>
                  </div>
                  <div class="property-item">
                    <el-upload
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :show-file-list="false"
                      :on-success="handleImageUpload"
                    >
                      <el-button type="primary" size="small">
                        <el-icon><Upload /></el-icon>
                        上传图片
                      </el-button>
                    </el-upload>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div v-else class="canvas-properties">
            <div class="panel-header">
              <h3 class="panel-title">画布属性</h3>
            </div>
            <div class="property-section">
              <div class="property-item">
                <label class="property-label">网格显示</label>
                <el-switch v-model="showGrid" />
              </div>
              <div class="property-item">
                <label class="property-label">背景颜色</label>
                <el-color-picker v-model="canvasBackground" size="small" show-alpha />
              </div>
              <div class="property-item">
                <label class="property-label">画布尺寸</label>
                <div class="canvas-size-controls">
                  <el-input-number
                    v-model="canvasSize.width"
                    size="small"
                    :min="400"
                    :max="2000"
                    controls-position="right"
                    placeholder="宽度"
                  />
                  <span class="size-separator">×</span>
                  <el-input-number
                    v-model="canvasSize.height"
                    size="small"
                    :min="300"
                    :max="2000"
                    controls-position="right"
                    placeholder="高度"
                  />
                </div>
              </div>
              <div class="property-item">
                <el-upload
                  action="https://jsonplaceholder.typicode.com/posts/"
                  :show-file-list="false"
                  :on-success="handleBackgroundUpload"
                >
                  <el-button type="primary" size="small">
                    <el-icon><Upload /></el-icon>
                    上传背景图
                  </el-button>
                </el-upload>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 操作提示 -->
    <el-notification
      v-model:visible="showNotification"
      :title="notificationMessage"
      :message="notificationDescription"
      :duration="3000"
      @close="showNotification = false"
    />

    <!-- 模板列表对话框 -->
    <el-dialog v-model="templateListVisible" title="模板列表" width="60%">
      <div class="grid grid-cols-3 gap-4">
        <div v-for="(template, index) in templateList" :key="index" class="border rounded p-3 cursor-pointer hover:bg-gray-50">
          <div class="text-center font-medium mb-2">{{ template.name }}</div>
          <div class="bg-gray-100 h-32 flex items-center justify-center mb-2">
            <img v-if="template.thumbnail" :src="template.thumbnail" class="max-h-full max-w-full" />
            <div v-else class="text-gray-400">无预览图</div>
          </div>
          <div class="flex justify-between">
            <el-button size="small" @click="loadTemplate(template)">使用</el-button>
            <el-button size="small" type="danger" @click="deleteTemplate(template, index)">删除</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 查看JSON对话框 -->
    <el-dialog v-model="jsonViewVisible" title="布局JSON" width="60%">
      <pre class="bg-gray-100 p-4 rounded overflow-auto max-h-96">{{ JSON.stringify(canvasItems, null, 2) }}</pre>
      <template #footer>
        <el-button @click="jsonViewVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyJSON">复制</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import axios from '@/api/request';
import {
  ElButton,
  ElButtonGroup,
  ElTabs,
  ElTabPane,
  ElInput,
  ElInputNumber,
  ElRadio,
  ElRadioGroup,
  ElColorPicker,
  ElSwitch,
  ElNotification,
  ElMessageBox,
  ElMessage,
  ElDialog,
  ElUpload,
  ElIcon
} from 'element-plus';
import {
  DocumentAdd,
  Collection,
  View,
  Expand,
  Fold,
  ZoomIn,
  ZoomOut,
  Upload
} from '@element-plus/icons-vue';

// 响应式状态
const isMobile = ref(false);
const leftPanelCollapsed = ref(false);
const rightPanelCollapsed = ref(false);

// 标签页状态
const activeTab = ref('1');
const activePropertyTab = ref('properties');
const selectedItem = ref<number | null>(null);

// 通知状态
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationDescription = ref('');

// 对话框状态
const templateListVisible = ref(false);
const jsonViewVisible = ref(false);

// 画布设置
const canvasSize = reactive({
  width: 800,
  height: 600
});
const showGrid = ref(false);
const canvasBackground = ref('#ffffff');
const canvasItems = reactive<any[]>([]);
const zoomLevel = ref(1);

// 预设图片
const presetImages = ref([
  'https://mastergo.com/ai/api/search-image?query=a modern minimalist product display with clean white background and soft lighting&width=300&height=300&orientation=squarish',
  'https://ai-public.mastergo.com/ai/img_res/4a2ebd8c6b66908dc063a1e6ec17ba30.jpg',
  'https://ai-public.mastergo.com/ai/img_res/0de19f9320912c2a696cb7e68194c401.jpg',
  'https://ai-public.mastergo.com/ai/img_res/d1202b3e18a76b46aec75f1c333a9387.jpg',
  'https://ai-public.mastergo.com/ai/img_res/d69101386742e3a72528e82a02b83b31.jpg',
  'https://ai-public.mastergo.com/ai/img_res/0011e6160b892365ae5f06d5300794b8.jpg'
]);

// 模板列表
const templateList = ref([
  { id: 1, name: '默认模板1', thumbnail: presetImages.value[0], data: [] },
  { id: 2, name: '默认模板2', thumbnail: presetImages.value[1], data: [] }
]);

// 组件库
const mediaComponents = [
  { name: '图片', type: 'image', icon: 'fas fa-image' },
  { name: '视频', type: 'video', icon: 'fas fa-video' },
  { name: 'Logo', type: 'logo', icon: 'fas fa-copyright' }
];

const infoComponents = [
  { name: '文本', type: 'text', icon: 'fas fa-font' },
  { name: '富文本', type: 'richtext', icon: 'fas fa-paragraph' },
  { name: '数字时钟', type: 'digitalclock', icon: 'fas fa-clock' },
  { name: '模拟时钟', type: 'analogclock', icon: 'fas fa-clock' },
  { name: '天气', type: 'weather', icon: 'fas fa-cloud-sun' },
  { name: '位置', type: 'location', icon: 'fas fa-map-marker-alt' },
  { name: '应用图标', type: 'appicon', icon: 'fas fa-mobile-alt' },
  { name: '滚动字幕', type: 'marquee', icon: 'fas fa-scroll' }
];

// 拖拽开始
const dragStart = (event: DragEvent, item: any) => {
  event.dataTransfer?.setData('text/plain', JSON.stringify(item));
};

// 放置组件
const drop = (event: DragEvent) => {
  event.preventDefault();
  const data = event.dataTransfer?.getData('text/plain');
  if (!data) return;

  const item = JSON.parse(data);
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  const x = event.clientX - rect.left - 50;
  const y = event.clientY - rect.top - 50;

  const newItem = {
    ...item,
    x,
    y,
    width: 100,
    height: 100,
    zIndex: canvasItems.length,
    content: '',
    fontSize: 16,
    color: '#000000',
    shape: 'rectangle',
    src: 'https://mastergo.com/ai/api/search-image?query=a modern minimalist product display with clean white background and soft lighting&width=300&height=300&orientation=squarish'
  };

  if (item.type === 'text') {
    newItem.content = '双击编辑文本';
  }

  canvasItems.push(newItem);
  showNotification.value = true;
  notificationMessage.value = '添加成功';
  notificationDescription.value = `已添加 ${item.name} 组件到画布`;
};

// 选择组件
const selectItem = (index: number) => {
  selectedItem.value = index;
};

// 删除组件确认
const showDeleteConfirm = (index: number) => {
  ElMessageBox.confirm('确定要删除这个组件吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    canvasItems.splice(index, 1);
    selectedItem.value = null;
    showNotification.value = true;
    notificationMessage.value = '删除成功';
    notificationDescription.value = '已从画布中移除该组件';
  }).catch(() => {});
};

// 图片上传处理
const handleImageUpload = (response: any) => {
  if (selectedItem.value !== null && canvasItems[selectedItem.value]) {
    // 实际项目中应该使用真实的上传接口返回的URL
    canvasItems[selectedItem.value].src = response.url || presetImages.value[0];
    ElMessage.success('图片上传成功');
  }
};

// 背景图片上传处理
const handleBackgroundUpload = (response: any) => {
  // 实际项目中应该使用真实的上传接口返回的URL
  ElMessage.success('背景图片上传成功');
};

// 保存主题
const saveTheme = () => {
  // 实际项目中应该调用API保存数据
  ElMessage.success('主题保存成功');
};

// 显示模板列表
const showTemplateList = () => {
  templateListVisible.value = true;
};

// 加载模板
const loadTemplate = (template: any) => {
  ElMessageBox.confirm('加载模板将覆盖当前画布内容，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实际项目中应该加载模板数据
    ElMessage.success(`已加载模板: ${template.name}`);
    templateListVisible.value = false;
  }).catch(() => {});
};

// 删除模板
const deleteTemplate = (template: any, index: number) => {
  ElMessageBox.confirm('确定要删除这个模板吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    templateList.value.splice(index, 1);
    ElMessage.success(`已删除模板: ${template.name}`);
  }).catch(() => {});
};

// 显示JSON视图
const showLayoutJSON = () => {
  jsonViewVisible.value = true;
};

// 复制JSON
const copyJSON = () => {
  const jsonStr = JSON.stringify(canvasItems, null, 2);
  navigator.clipboard.writeText(jsonStr).then(() => {
    ElMessage.success('JSON已复制到剪贴板');
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制');
  });
};

// 响应式处理
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768;
  if (isMobile.value) {
    leftPanelCollapsed.value = true;
    rightPanelCollapsed.value = true;
  }
};

const toggleLeftPanel = () => {
  leftPanelCollapsed.value = !leftPanelCollapsed.value;
};

const toggleRightPanel = () => {
  rightPanelCollapsed.value = !rightPanelCollapsed.value;
};

// 画布缩放功能
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.2) {
    zoomLevel.value = Math.max(0.2, zoomLevel.value - 0.1);
  }
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

// 生命周期
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});
</script>

<style scoped lang="scss">
/* 主题管理页面样式 */
.theme-management {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* 顶部工具栏 */
.top-toolbar {
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 主工作区 */
.main-workspace {
  flex: 1;
  display: flex;
  height: calc(100vh - 60px);
  overflow: hidden;
}

/* 左侧组件库面板 */
.left-panel {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  &.collapsed {
    width: 0;
    overflow: hidden;
  }
}

/* 右侧属性面板 */
.right-panel {
  width: 320px;
  background: #ffffff;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  &.collapsed {
    width: 0;
    overflow: hidden;
  }
}

/* 面板头部 */
.panel-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  background: #fafbfc;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 面板内容 */
.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 组件网格 */
.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 12px;
}

.component-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;

  &:hover {
    border-color: #409eff;
    background: #f0f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  }
}

.component-icon {
  font-size: 20px;
  color: #606266;
  margin-bottom: 6px;
  display: block;
}

.component-name {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
}

/* 画布区域 */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  min-width: 600px;
}

.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.canvas-toolbar {
  height: 40px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;
  color: #606266;
}

.canvas-info {
  font-weight: 500;
}

.canvas-viewport {
  flex: 1;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f5f7fa;
}

.canvas {
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
  transform-origin: center;
  transition: transform 0.2s ease;
}

/* 画布项目 */
.canvas-item {
  position: absolute;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #409eff;
  }

  &.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

.item-content {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 2px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.item-header {
  background: #f5f7fa;
  padding: 4px 8px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-label {
  font-size: 10px;
  color: #606266;
  font-weight: 500;
}

.item-body {
  padding: 8px;
  height: calc(100% - 24px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-content {
  width: 100%;
  word-break: break-all;
}

.image-content {
  max-width: 100%;
  max-height: 100%;
}

/* 属性配置 */
.property-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.property-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.property-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.property-label {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

/* 预设图片 */
.preset-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.preset-image-item {
  aspect-ratio: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #409eff;
    transform: scale(1.02);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

/* 画布尺寸控制 */
.canvas-size-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.size-separator {
  color: #909399;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-workspace {
    position: relative;
  }

  .left-panel,
  .right-panel {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 200;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }

  .left-panel {
    left: 0;
  }

  .right-panel {
    right: 0;
  }

  .canvas-area {
    min-width: auto;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-tabs__header) {
  margin-bottom: 12px;
}

:deep(.el-tabs__item) {
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  font-size: 12px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-color-picker) {
  width: 100%;
}

:deep(.component-tabs .el-tabs__content) {
  padding: 0;
}

:deep(.property-tabs .el-tabs__content) {
  padding: 0;
}

:deep(.el-button-group .el-button) {
  padding: 4px 8px;
  font-size: 12px;
}
</style>