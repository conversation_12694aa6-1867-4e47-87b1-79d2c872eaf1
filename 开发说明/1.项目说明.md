# 项目说明

## 项目名称

爱视影音云端管理

## 技术栈说明

软件版本: PHP 8.0 Mysql 8.0 Vue 3.x ElementUI 2.x  
编辑器:   Phpstorm

## 目录说明

    项目根目录
        |- API                  // 子服务器访问接口
        |- Modules         // 公共核心功能,非公共功能放入对应的模块
            |-- Models          // 公共数据表模型
            |-- Consts          // 公共常量定义
            |-- Entities        // 模型对应实体
            |-- Hooks           // 框架钩子目录
            |-- Rules           // 公共数据类型格式校验
            |-- Uploads         // 上传处理模块
            |-- Utility         // 公共工具类
            |-- Validations     // 模型数据验证类
            |-- Logics          // 公共业务逻辑
            |-- Baseconfig.php  // 公共配置类    --- 即将废弃,不再写入新代码
        |- public               // 网站根目录
        |- system               // 框架核心目录
        |- vendor               // 第三方composer存储路径
        |- VueWdmin             // 后台前端界面
        |- Wdmin                // 后台管理接口
        |- writable             // 框架公共日志,文件缓存目录
        |- 开发说明              // 项目说明,开发注意事项

## 测试环境

### 访问地址

    URL: http://test.asmovie.cn/

### FTP账号资料

    用户：test_asmovie_cn  
    密码：ffCpk43a3cTN2N28

### 数据库账号资料

    数据库名：test_asmovie_cn  
    用户：test_asmovie_cn  
    密码：78nhHTfMBFJa2CMG  